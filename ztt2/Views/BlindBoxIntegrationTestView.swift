//
//  BlindBoxIntegrationTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒配置与动画集成测试视图
 * 验证用户配置的奖品能够在盲盒中正确显示和抽奖
 */
struct BlindBoxIntegrationTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var selectedMember: Member?
    @State private var showBlindBox = false
    @State private var testResults: [String] = []
    @State private var isRunningTest = false
    @State private var showMemberSelection = false
    
    // 测试配置数据
    private let testConfigs = [
        TestConfig(
            name: "基础配置",
            boxCount: 3,
            costPerPlay: 10,
            prizes: ["小红花", "贴纸", "铅笔"]
        ),
        TestConfig(
            name: "中等配置",
            boxCount: 5,
            costPerPlay: 15,
            prizes: ["奖状", "文具盒", "彩色笔", "橡皮", "尺子"]
        ),
        TestConfig(
            name: "大型配置",
            boxCount: 8,
            costPerPlay: 20,
            prizes: ["图书", "玩具", "零食", "贴画", "徽章", "钥匙扣", "小礼品", "惊喜盒"]
        )
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("盲盒配置集成测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 测试说明
                testInstructionSection
                
                // 成员选择
                memberSelectionSection
                
                // 测试配置列表
                testConfigSection
                
                // 测试结果
                if !testResults.isEmpty {
                    testResultsSection
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .background(Color.gray.opacity(0.05))
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showMemberSelection) {
            memberSelectionSheet
        }
        .fullScreenCover(isPresented: $showBlindBox) {
            if let member = selectedMember {
                NavigationView {
                    BlindBoxView(
                        member: member,
                        onDismiss: {
                            showBlindBox = false
                        },
                        onNavigateToSettings: {
                            // 返回配置界面
                            showBlindBox = false
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
    }
    
    // MARK: - Test Instruction Section
    
    private var testInstructionSection: some View {
        VStack(spacing: 12) {
            Text("测试说明")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text("1. 选择一个测试成员\n2. 选择测试配置并应用\n3. 打开盲盒页面验证配置\n4. 测试开箱功能和奖品显示")
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineSpacing(4)
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Member Selection Section
    
    private var memberSelectionSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("选择测试成员")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                Button(action: {
                    showMemberSelection = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "person.circle")
                            .font(.system(size: 16, weight: .medium))
                        
                        Text(selectedMember?.displayName ?? "选择成员")
                            .font(.system(size: 14, weight: .medium))
                    }
                    .foregroundColor(DesignSystem.Colors.primary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(DesignSystem.Colors.primary.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(DesignSystem.Colors.primary, lineWidth: 1)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            if let member = selectedMember {
                HStack {
                    Text("当前积分: \(Int(member.currentPoints))")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Spacer()
                    
                    Button(action: {
                        addTestPoints()
                    }) {
                        Text("添加100积分")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.orange)
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    // MARK: - Test Config Section
    
    private var testConfigSection: some View {
        VStack(spacing: 16) {
            Text("测试配置")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            ForEach(testConfigs, id: \.name) { config in
                testConfigCard(config: config)
            }
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func testConfigCard(config: TestConfig) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(config.name)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("\(config.boxCount)个盲盒 • \(config.costPerPlay)积分/次")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                Button(action: {
                    applyTestConfig(config)
                }) {
                    Text("应用配置")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(DesignSystem.Colors.primary)
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(selectedMember == nil || isRunningTest)
            }
            
            Text("奖品: \(config.prizes.joined(separator: "、"))")
                .font(.system(size: 11, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .lineLimit(2)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
        )
    }
    
    // MARK: - Test Results Section
    
    private var testResultsSection: some View {
        VStack(spacing: 12) {
            Text("测试结果")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        HStack(alignment: .top, spacing: 8) {
                            Text("\(index + 1).")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .frame(width: 20, alignment: .leading)
                            
                            Text(result)
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                .multilineTextAlignment(.leading)
                            
                            Spacer()
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
            .frame(maxHeight: 200)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.green.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.green.opacity(0.3), lineWidth: 1)
                    )
            )
            
            Button(action: {
                testResults.removeAll()
            }) {
                Text("清除结果")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.red)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    // MARK: - Member Selection Sheet
    
    private var memberSelectionSheet: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("选择测试成员")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                if dataManager.members.isEmpty {
                    Text("暂无成员，请先添加成员")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .padding(.vertical, 40)
                } else {
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(dataManager.members, id: \.objectID) { member in
                                memberSelectionCard(member: member)
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                }
                
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        showMemberSelection = false
                    }
                }
            }
        }
    }
    
    private func memberSelectionCard(member: Member) -> some View {
        Button(action: {
            selectedMember = member
            showMemberSelection = false
            addTestResult("选择成员: \(member.displayName)")
        }) {
            HStack(spacing: 12) {
                Circle()
                    .fill(DesignSystem.Colors.primary)
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(String(member.displayName.prefix(1)))
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(member.displayName)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("\(Int(member.currentPoints))积分")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                if selectedMember?.objectID == member.objectID {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.primary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary.opacity(0.1) : Color.gray.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.clear, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Test Actions
    
    private func applyTestConfig(_ config: TestConfig) {
        guard let member = selectedMember else {
            addTestResult("❌ 未选择成员")
            return
        }
        
        isRunningTest = true
        
        // 保存配置
        let savedConfig = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: config.boxCount,
            costPerPlay: config.costPerPlay,
            boxPrizes: config.prizes
        )
        
        if savedConfig != nil {
            addTestResult("✅ 应用配置: \(config.name)")
            addTestResult("   盲盒数量: \(config.boxCount)")
            addTestResult("   消耗积分: \(config.costPerPlay)")
            addTestResult("   奖品列表: \(config.prizes.joined(separator: "、"))")
            
            // 验证配置加载
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                verifyConfigIntegration(config)
            }
        } else {
            addTestResult("❌ 配置应用失败: \(config.name)")
        }
        
        isRunningTest = false
    }
    
    private func verifyConfigIntegration(_ config: TestConfig) {
        guard let member = selectedMember else { return }
        
        // 验证配置加载
        if let loadedConfig = dataManager.getBlindBoxConfig(for: member) {
            addTestResult("✅ 配置加载成功")
            addTestResult("   加载的盲盒数量: \(loadedConfig.itemCount)")
            addTestResult("   加载的消耗积分: \(loadedConfig.costPerPlay)")
            
            let loadedPrizes = loadedConfig.allItems.map { $0.formattedPrizeName }
            addTestResult("   加载的奖品: \(loadedPrizes.joined(separator: "、"))")
            
            // 验证数据一致性
            if Int(loadedConfig.itemCount) == config.boxCount &&
               Int(loadedConfig.costPerPlay) == config.costPerPlay &&
               loadedPrizes == config.prizes {
                addTestResult("✅ 数据一致性验证通过")
                
                // 提示可以测试盲盒功能
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    addTestResult("🎁 可以打开盲盒页面测试开箱功能")
                    showBlindBox = true
                }
            } else {
                addTestResult("❌ 数据一致性验证失败")
            }
        } else {
            addTestResult("❌ 配置加载失败")
        }
    }
    
    private func addTestPoints() {
        guard let member = selectedMember else { return }
        
        let newPoints = Int(member.currentPoints) + 100
        member.currentPoints = Int32(newPoints)
        dataManager.save()
        
        addTestResult("✅ 为 \(member.displayName) 添加100积分，当前积分: \(newPoints)")
    }
    
    private func addTestResult(_ result: String) {
        testResults.append(result)
    }
}

// MARK: - Test Config Model

struct TestConfig {
    let name: String
    let boxCount: Int
    let costPerPlay: Int
    let prizes: [String]
}

#Preview {
    BlindBoxIntegrationTestView()
        .environmentObject(DataManager.shared)
}
