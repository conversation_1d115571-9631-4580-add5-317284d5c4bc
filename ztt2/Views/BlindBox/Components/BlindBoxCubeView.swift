//
//  BlindBoxCubeView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 3D立方体盲盒组件
 * 实现立方体的正面、背面和侧面渲染，支持3D变换和材质效果
 */
struct BlindBoxCubeView: View {
    
    // MARK: - Properties
    let boxItem: BlindBoxItem
    let size: CGFloat
    let onTap: () -> Void
    
    // MARK: - Animation State
    @State private var rotationAngle: Double = 0
    @State private var isPressed = false
    
    // MARK: - Constants
    private let cubeDepth: CGFloat = 20
    private let shadowRadius: CGFloat = 8
    private let shadowOffset: CGFloat = 4
    
    var body: some View {
        ZStack {
            // 主立方体
            cubeBody
                .rotation3DEffect(
                    .degrees(rotationAngle),
                    axis: (x: 0.2, y: 1.0, z: 0.1),
                    anchor: .center,
                    perspective: 0.8
                )
                .scaleEffect(boxItem.scaleEffect * (isPressed ? 0.95 : 1.0))
                .opacity(boxItem.opacity)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
                .animation(.easeInOut(duration: 0.8), value: boxItem.explosionState)
                .onTapGesture {
                    if boxItem.isClickable {
                        // 触觉反馈
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                        
                        // 点击动画
                        withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                            rotationAngle += 360
                        }
                        
                        onTap()
                    }
                }
                .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                    isPressed = pressing && boxItem.isClickable
                }, perform: {})
            
            // 爆炸效果遮罩
            if boxItem.explosionState == .exploding {
                explosionOverlay
            }
        }
        .frame(width: size, height: size)
    }
    
    // MARK: - Cube Body
    
    private var cubeBody: some View {
        ZStack {
            // 只保留正面，移除3D效果的顶面和右侧面以及背景阴影
            frontFace
        }
    }
    
    // MARK: - Cube Faces
    
    /**
     * 正面 - 主要可见面
     */
    private var frontFace: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(Color.clear)
            .background(
                // 背景图片
                Image(boxItem.isOpened ? "宝箱已打开" : "宝箱未打开")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: size, height: size)
                    .clipped()
                    .cornerRadius(16)
            )
            .overlay(
                // 边框
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        boxItem.isOpened ? Color.green.opacity(0.6) : Color.blue.opacity(0.4),
                        lineWidth: 0
                    )
            )
    }
    
    // MARK: - Face Content
    
    /**
     * 盲盒图标（未开启状态）
     */
    private var blindBoxIcon: some View {
        VStack(spacing: 6) {
            ZStack {
                // 黑色描边
                Image(systemName: "shippingbox.fill")
                    .font(.system(size: size * 0.3, weight: .bold))
                    .foregroundColor(.black)
                    .offset(x: 2, y: 2)
                
                // 主图标
                Image(systemName: "shippingbox.fill")
                    .font(.system(size: size * 0.3, weight: .bold))
                    .foregroundColor(.white)
            }
            .shadow(color: .black.opacity(0.8), radius: 6, x: 0, y: 0)
            
            ZStack {
                // 黑色描边
                Text("?")
                    .font(.system(size: size * 0.25, weight: .heavy))
                    .foregroundColor(.black)
                    .offset(x: 2, y: 2)
                
                // 主文字
                Text("?")
                    .font(.system(size: size * 0.25, weight: .heavy))
                    .foregroundColor(.white)
            }
            .shadow(color: .black.opacity(0.8), radius: 6, x: 0, y: 0)
        }
    }
    
    /**
     * 奖品显示（已开启状态）
     */
    private var prizeDisplay: some View {
        VStack(spacing: 4) {
            // 奖品图标
            ZStack {
                // 黑色描边
                Image(systemName: "gift.fill")
                    .font(.system(size: size * 0.25, weight: .bold))
                    .foregroundColor(.black)
                    .offset(x: 2, y: 2)
                
                // 主图标
                Image(systemName: "gift.fill")
                    .font(.system(size: size * 0.25, weight: .bold))
                    .foregroundColor(.yellow)
            }
            .shadow(color: .black.opacity(0.8), radius: 6, x: 0, y: 0)
            
            // 奖品名称
            ZStack {
                // 黑色描边
                Text(boxItem.prizeName)
                    .font(.system(size: size * 0.12, weight: .bold))
                    .foregroundColor(.black)
                    .offset(x: 1, y: 1)
                
                // 主文字
                Text(boxItem.prizeName)
                    .font(.system(size: size * 0.12, weight: .bold))
                    .foregroundColor(.white)
            }
            .shadow(color: .black.opacity(0.8), radius: 4, x: 0, y: 0)
            .multilineTextAlignment(.center)
            .lineLimit(2)
        }
    }
    
    // MARK: - Gradients
    
    /**
     * 未开启状态渐变
     */
    private var unopenedFaceGradient: LinearGradient {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primary.opacity(0.3),
                DesignSystem.Colors.secondary.opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    /**
     * 已开启状态渐变
     */
    private var openedFaceGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color.green.opacity(0.3),
                Color.blue.opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - Explosion Overlay
    
    /**
     * 爆炸效果遮罩
     */
    private var explosionOverlay: some View {
        ZStack {
            // 白色闪光
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.8))
                .scaleEffect(1.2)
                .blur(radius: 4)
            
            // 能量环
            Circle()
                .stroke(Color.yellow, lineWidth: 3)
                .scaleEffect(1.5)
                .opacity(0.7)
                .blur(radius: 1)
        }
        .animation(.easeOut(duration: 0.3), value: boxItem.explosionState)
    }
}

// MARK: - Floating Animation Modifier

struct FloatingModifier: ViewModifier {
    let offset: CGFloat
    let rotation: Double
    let delay: Double
    let duration: Double
    let enabled: Bool
    
    @State private var isFloating = false
    
    func body(content: Content) -> some View {
        content
            .offset(y: enabled && isFloating ? -offset : 0)
            .rotationEffect(.degrees(enabled && isFloating ? rotation : 0))
            .animation(
                .easeInOut(duration: duration)
                .repeatForever(autoreverses: true)
                .delay(delay),
                value: isFloating
            )
            .onAppear {
                if enabled {
                    isFloating = true
                }
            }
            .onChange(of: enabled) { newValue in
                if newValue {
                    isFloating = true
                } else {
                    isFloating = false
                }
            }
    }
}

extension View {
    func floating(
        offset: CGFloat = 8,
        rotation: Double = 3,
        delay: Double = 0,
        duration: Double = 2.0,
        enabled: Bool = true
    ) -> some View {
        modifier(FloatingModifier(
            offset: offset,
            rotation: rotation,
            delay: delay,
            duration: duration,
            enabled: enabled
        ))
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // 未开启的盲盒
        BlindBoxCubeView(
            boxItem: BlindBoxItem.create(index: 0, prizeName: "神秘奖品"),
            size: 120,
            onTap: {}
        )
        
        // 已开启的盲盒
        BlindBoxCubeView(
            boxItem: {
                var item = BlindBoxItem.create(index: 1, prizeName: "小红花")
                item.completeOpening()
                return item
            }(),
            size: 120,
            onTap: {}
        )
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
