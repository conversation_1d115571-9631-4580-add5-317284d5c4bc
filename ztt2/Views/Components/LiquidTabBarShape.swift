//
//  LiquidTabBarShape.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//  手动路径绘制实现矩形+圆形组合
//

import SwiftUI

/**
 * 液态导航栏Shape - 手动路径绘制实现完美圆弧
 * 设计理念：通过精确数学计算，手动绘制矩形与圆形的组合轮廓
 */
struct LiquidTabBarShape: Shape {
    
    // MARK: - Properties
    
    /// 融球中心X坐标
    var bubbleCenter: CGFloat
    
    /// 过渡进度（0.0-1.0）
    var transitionProgress: CGFloat = 1.0
    
    // MARK: - Constants
    
    /// 圆形上拱半径
    private let archRadius: CGFloat = 45.0
    
    /// 圆形与矩形重合深度
    private let overlapDepth: CGFloat = 10.0
    
    /// 圆心垂直偏移（正值向下移动圆心，负值向上移动）
    private let circleCenterYOffset: CGFloat = 60.0
    
    // MARK: - Animatable
    
    var animatableData: AnimatablePair<CGFloat, CGFloat> {
        get {
            AnimatablePair(bubbleCenter, transitionProgress)
        }
        set {
            bubbleCenter = newValue.first
            transitionProgress = newValue.second
        }
    }
    
    // MARK: - Shape Protocol
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // 当没有过渡进度时，直接绘制普通矩形
        guard transitionProgress > 0 else {
            path.addRect(rect)
            return path
        }
        
        // 1. 计算几何参数
        let circleRadius = archRadius * transitionProgress
        let circleCenterX = bubbleCenter
        let circleCenterY = -circleRadius + overlapDepth + circleCenterYOffset
        
        // 2. 检查圆是否与矩形顶边相交
        guard abs(circleCenterY) <= circleRadius else {
            // 圆不与顶边相交，绘制普通矩形
            path.addRect(rect)
            return path
        }
        
        // 3. 计算圆与矩形顶边的交点
        let deltaX = sqrt(circleRadius * circleRadius - circleCenterY * circleCenterY)
        let leftIntersectionX = max(0, circleCenterX - deltaX)  // 确保不超出左边界
        let rightIntersectionX = min(rect.width, circleCenterX + deltaX)  // 确保不超出右边界
        
        // 4. 计算圆弧的角度
        let leftAngle = atan2(-circleCenterY, leftIntersectionX - circleCenterX)
        let rightAngle = atan2(-circleCenterY, rightIntersectionX - circleCenterX)
        
        // 5. 开始绘制完整的连续路径
        
        // 起点：矩形左上角
        path.move(to: CGPoint(x: 0, y: 0))
        
        // 左侧顶边：从左上角到左交点
        if leftIntersectionX > 0 {
            path.addLine(to: CGPoint(x: leftIntersectionX, y: 0))
        }
        
        // 圆弧段：从左交点到右交点（逆时针方向，形成上拱）
        if leftIntersectionX < rightIntersectionX {
            let circleCenter = CGPoint(x: circleCenterX, y: circleCenterY)
            
            // 使用addArc绘制圆弧（从左角度到右角度，逆时针）
            path.addArc(
                center: circleCenter,
                radius: circleRadius,
                startAngle: Angle(radians: leftAngle),
                endAngle: Angle(radians: rightAngle),
                clockwise: false  // 逆时针绘制上拱效果
            )
        }
        
        // 右侧顶边：从右交点到矩形右上角
        if rightIntersectionX < rect.width {
            path.addLine(to: CGPoint(x: rect.width, y: 0))
        }
        
        // 右边界：从右上角到右下角
        path.addLine(to: CGPoint(x: rect.width, y: rect.height))
        
        // 底边：从右下角到左下角
        path.addLine(to: CGPoint(x: 0, y: rect.height))
        
        // 左边界：从左下角回到起点
        path.addLine(to: CGPoint(x: 0, y: 0))
        
        // 闭合路径
        path.closeSubpath()
        
        return path
    }
}

// MARK: - Preview
#Preview {
    VStack {
        Spacer()
        
        ZStack {
            LiquidTabBarShape(bubbleCenter: 100, transitionProgress: 1.0)
                .fill(Color.white)
            
            LiquidTabBarShape(bubbleCenter: 100, transitionProgress: 1.0)
                .stroke(Color(hex: "#a9d051"), lineWidth: 5)
        }
        .frame(height: 72)
        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: -4)
    }
    .background(Color.gray.opacity(0.2) as Color)
}
