//
//  FamilyMemberGridView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 家庭成员网格视图组件
 * 基于参考项目设计，保持完全一致的UI和交互
 */
struct FamilyMemberGridView: View {

    // 示例数据结构
    struct FamilyMember {
        let id: String
        let name: String
        let role: String
        let currentPoints: Int
    }
    
    let members: [FamilyMember]
    let isDeleteMode: Bool
    let onMemberTapped: (FamilyMember) -> Void
    let onEnterDeleteMode: () -> Void
    let onExitDeleteMode: () -> Void
    let onDeleteRequested: (FamilyMember) -> Void
    let onRefresh: () async -> Void // 下拉刷新回调
    
    // 网格列配置
    private let columns = [
        GridItem(.flexible(), spacing: DesignSystem.Spacing.md),
        GridItem(.flexible(), spacing: DesignSystem.Spacing.md)
    ]
    
    var body: some View {
        ScrollView {
            if members.isEmpty {
                // 空状态视图 - 没有家庭成员
                VStack(spacing: DesignSystem.Spacing.lg) {
                    VStack(spacing: DesignSystem.Spacing.md) {
                        Image(systemName: "person.2")
                            .font(.system(size: 60))
                            .foregroundColor(DesignSystem.Colors.textTertiary)

                        Text("还没有家庭成员")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)

                        Text("点击右上角添加第一个成员吧")
                            .font(.system(size: 14))
                            .foregroundColor(DesignSystem.Colors.textTertiary)
                            .multilineTextAlignment(.center)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, 60)
            } else {
                LazyVGrid(columns: columns, spacing: DesignSystem.Spacing.md) {
                    ForEach(Array(members.enumerated()), id: \.element.id) { index, member in
                        FamilyMemberCardView(
                            memberName: member.name,
                            memberRole: member.role,
                            currentPoints: member.currentPoints,
                            memberIndex: index + 1,  // 从1开始编号
                            isDeleteMode: isDeleteMode,
                            onLongPress: {
                                onEnterDeleteMode()
                            },
                            onDeleteTapped: {
                                onDeleteRequested(member)
                            },
                            onCardTapped: {
                                if isDeleteMode {
                                    onExitDeleteMode()
                                } else {
                                    onMemberTapped(member)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, DesignSystem.Spacing.xs)
            }
        }
        .refreshable {
            // 执行下拉刷新
            await onRefresh()
        }
    }
}

// MARK: - Preview
#Preview {
    VStack {
        // 空状态预览
        FamilyMemberGridView(
            members: [],
            isDeleteMode: false,
            onMemberTapped: { member in
                print("点击了家庭成员: \(member.name)")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { member in
                print("请求删除家庭成员: \(member.name)")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}

#Preview("Empty State") {
    VStack {
        // 空状态（无成员）
        FamilyMemberGridView(
            members: [],
            isDeleteMode: false,
            onMemberTapped: { member in
                print("点击了家庭成员: \(member.name)")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { member in
                print("请求删除家庭成员: \(member.name)")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}

#Preview("With Members") {
    // 创建示例家庭成员
    let sampleMembers = [
        FamilyMemberGridView.FamilyMember(
            id: "1",
            name: "爸爸",
            role: "father",
            currentPoints: 0
        ),
        FamilyMemberGridView.FamilyMember(
            id: "2",
            name: "妈妈",
            role: "mother",
            currentPoints: 0
        ),
        FamilyMemberGridView.FamilyMember(
            id: "3",
            name: "多多",
            role: "son",
            currentPoints: 10
        ),
        FamilyMemberGridView.FamilyMember(
            id: "4",
            name: "朵朵",
            role: "daughter",
            currentPoints: 5
        )
    ]

    VStack {
        FamilyMemberGridView(
            members: sampleMembers,
            isDeleteMode: false,
            onMemberTapped: { member in
                print("点击了家庭成员: \(member.name)")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { member in
                print("请求删除家庭成员: \(member.name)")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}
