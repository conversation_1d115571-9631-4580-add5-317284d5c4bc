//
//  BlindBoxConfigIntegrationTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒配置集成验证测试视图
 * 验证从配置到使用的完整流程
 */
struct BlindBoxConfigIntegrationTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var selectedMember: Member?
    @State private var testStep = 0
    @State private var testResults: [TestResult] = []
    @State private var isRunningTest = false
    @State private var showBlindBox = false
    
    // 测试配置
    private let testConfig = TestConfig(
        name: "集成测试配置",
        boxCount: 4,
        costPerPlay: 15,
        prizes: ["测试奖品A", "测试奖品B", "测试奖品C", "测试奖品D"]
    )
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("盲盒配置集成验证")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 测试进度
                testProgressSection
                
                // 成员选择
                memberSelectionSection
                
                // 测试步骤
                testStepsSection
                
                // 测试结果
                if !testResults.isEmpty {
                    testResultsSection
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .background(Color.gray.opacity(0.05))
            .navigationBarHidden(true)
        }
        .fullScreenCover(isPresented: $showBlindBox) {
            if let member = selectedMember {
                NavigationView {
                    BlindBoxView(
                        member: member,
                        onDismiss: {
                            showBlindBox = false
                            completeStep(6, success: true, message: "盲盒页面正常显示和使用")
                        },
                        onNavigateToSettings: {
                            showBlindBox = false
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
    }
    
    // MARK: - Test Progress Section
    
    private var testProgressSection: some View {
        VStack(spacing: 12) {
            Text("测试进度")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            HStack(spacing: 8) {
                ForEach(1...6, id: \.self) { step in
                    Circle()
                        .fill(stepColor(for: step))
                        .frame(width: 12, height: 12)
                        .overlay(
                            Text("\(step)")
                                .font(.system(size: 8, weight: .bold))
                                .foregroundColor(.white)
                        )
                    
                    if step < 6 {
                        Rectangle()
                            .fill(step < testStep ? Color.green : Color.gray.opacity(0.3))
                            .frame(width: 20, height: 2)
                    }
                }
            }
            
            Text(stepDescription(for: testStep))
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func stepColor(for step: Int) -> Color {
        if step < testStep {
            return .green
        } else if step == testStep {
            return .blue
        } else {
            return .gray.opacity(0.3)
        }
    }
    
    private func stepDescription(for step: Int) -> String {
        switch step {
        case 0: return "准备开始测试"
        case 1: return "选择测试成员"
        case 2: return "保存盲盒配置"
        case 3: return "验证配置加载"
        case 4: return "验证数据一致性"
        case 5: return "验证盲盒生成"
        case 6: return "验证完整功能"
        default: return "测试完成"
        }
    }
    
    // MARK: - Member Selection Section
    
    private var memberSelectionSection: some View {
        VStack(spacing: 16) {
            Text("选择测试成员")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            if dataManager.members.isEmpty {
                Text("暂无成员，请先添加成员")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding(.vertical, 20)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(dataManager.members, id: \.objectID) { member in
                            memberCard(member: member)
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func memberCard(member: Member) -> some View {
        VStack(spacing: 8) {
            Circle()
                .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.gray.opacity(0.3))
                .frame(width: 50, height: 50)
                .overlay(
                    Text(String(member.displayName.prefix(1)))
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                )
            
            Text(member.displayName)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1)
            
            Text("\(Int(member.currentPoints))积分")
                .font(.system(size: 10, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary.opacity(0.1) : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .onTapGesture {
            selectedMember = member
            if testStep == 0 {
                completeStep(1, success: true, message: "选择成员: \(member.displayName)")
            }
        }
    }
    
    // MARK: - Test Steps Section
    
    private var testStepsSection: some View {
        VStack(spacing: 16) {
            Text("测试步骤")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(spacing: 12) {
                testStepButton(
                    step: 2,
                    title: "保存测试配置",
                    description: "保存盲盒配置到数据库",
                    action: saveTestConfig
                )
                
                testStepButton(
                    step: 3,
                    title: "验证配置加载",
                    description: "验证配置能够正确加载",
                    action: verifyConfigLoading
                )
                
                testStepButton(
                    step: 4,
                    title: "验证数据一致性",
                    description: "验证保存和加载的数据一致",
                    action: verifyDataConsistency
                )
                
                testStepButton(
                    step: 5,
                    title: "验证盲盒生成",
                    description: "验证盲盒项目正确生成",
                    action: verifyBlindBoxGeneration
                )
                
                testStepButton(
                    step: 6,
                    title: "测试完整功能",
                    description: "打开盲盒页面测试完整功能",
                    action: testCompleteFunction
                )
            }
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func testStepButton(
        step: Int,
        title: String,
        description: String,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Circle()
                    .fill(stepColor(for: step))
                    .frame(width: 30, height: 30)
                    .overlay(
                        Text("\(step)")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(description)
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                if step <= testStep {
                    Image(systemName: step < testStep ? "checkmark.circle.fill" : "play.circle.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(step < testStep ? .green : .blue)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.05))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(step > testStep + 1 || isRunningTest || selectedMember == nil)
    }
    
    // MARK: - Test Results Section
    
    private var testResultsSection: some View {
        VStack(spacing: 12) {
            Text("测试结果")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(testResults, id: \.id) { result in
                        testResultRow(result: result)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
            .frame(maxHeight: 200)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.05))
            )
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func testResultRow(result: TestResult) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: result.success ? "checkmark.circle.fill" : "xmark.circle.fill")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(result.success ? .green : .red)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("步骤\(result.step): \(result.message)")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                if let details = result.details {
                    Text(details)
                        .font(.system(size: 10, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            
            Spacer()
        }
    }
    
    // MARK: - Test Actions
    
    private func saveTestConfig() {
        guard let member = selectedMember else { return }
        
        isRunningTest = true
        
        let savedConfig = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: testConfig.boxCount,
            costPerPlay: testConfig.costPerPlay,
            boxPrizes: testConfig.prizes
        )
        
        if savedConfig != nil {
            completeStep(2, success: true, message: "配置保存成功", details: "盲盒数量: \(testConfig.boxCount), 积分: \(testConfig.costPerPlay)")
        } else {
            completeStep(2, success: false, message: "配置保存失败")
        }
        
        isRunningTest = false
    }
    
    private func verifyConfigLoading() {
        guard let member = selectedMember else { return }
        
        isRunningTest = true
        
        if let loadedConfig = dataManager.getBlindBoxConfig(for: member) {
            completeStep(3, success: true, message: "配置加载成功", details: "加载的盲盒数量: \(loadedConfig.itemCount), 积分: \(loadedConfig.costPerPlay)")
        } else {
            completeStep(3, success: false, message: "配置加载失败")
        }
        
        isRunningTest = false
    }
    
    private func verifyDataConsistency() {
        guard let member = selectedMember else { return }
        
        isRunningTest = true
        
        if let loadedConfig = dataManager.getBlindBoxConfig(for: member) {
            let isConsistent = Int(loadedConfig.itemCount) == testConfig.boxCount &&
                              Int(loadedConfig.costPerPlay) == testConfig.costPerPlay
            
            if isConsistent {
                completeStep(4, success: true, message: "数据一致性验证通过")
            } else {
                completeStep(4, success: false, message: "数据一致性验证失败", details: "配置数据与保存数据不匹配")
            }
        } else {
            completeStep(4, success: false, message: "无法加载配置进行一致性验证")
        }
        
        isRunningTest = false
    }
    
    private func verifyBlindBoxGeneration() {
        guard let member = selectedMember else { return }
        
        isRunningTest = true
        
        let viewModel = BlindBoxViewModel(member: member)
        viewModel.loadBlindBoxConfig()
        
        let validation = viewModel.validateConfigIntegrity()
        
        if validation.isValid {
            completeStep(5, success: true, message: "盲盒生成验证通过", details: "生成了\(viewModel.boxItems.count)个盲盒项目")
        } else {
            completeStep(5, success: false, message: "盲盒生成验证失败", details: validation.errors.joined(separator: ", "))
        }
        
        isRunningTest = false
    }
    
    private func testCompleteFunction() {
        guard selectedMember != nil else { return }
        
        // 确保成员有足够积分
        if let member = selectedMember {
            member.currentPoints = Int32(testConfig.costPerPlay * 10) // 确保有足够积分
            dataManager.save()
        }
        
        showBlindBox = true
    }
    
    private func completeStep(_ step: Int, success: Bool, message: String, details: String? = nil) {
        let result = TestResult(step: step, success: success, message: message, details: details)
        testResults.append(result)
        
        if success && step == testStep + 1 {
            testStep = step
        }
    }
}

// MARK: - Test Result Model

struct TestResult: Identifiable {
    let id = UUID()
    let step: Int
    let success: Bool
    let message: String
    let details: String?
}

#Preview {
    BlindBoxConfigIntegrationTestView()
        .environmentObject(DataManager.shared)
}
