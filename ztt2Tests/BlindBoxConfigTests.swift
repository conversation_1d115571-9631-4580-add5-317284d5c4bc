//
//  BlindBoxConfigTests.swift
//  ztt2Tests
//
//  Created by Augment Agent on 2025/7/31.
//

import XCTest
import CoreData
@testable import ztt2

/**
 * 盲盒配置功能测试类
 * 测试盲盒配置的保存、加载、更新、验证等功能
 */
final class BlindBoxConfigTests: XCTestCase {
    
    var persistenceController: PersistenceController!
    var testContext: NSManagedObjectContext!
    var dataManager: DataManager!
    var testMember: Member!
    
    override func setUpWithError() throws {
        // 使用内存存储进行测试
        persistenceController = PersistenceController(inMemory: true)
        testContext = persistenceController.container.viewContext
        
        // 创建测试用的DataManager实例
        dataManager = DataManager.shared
        dataManager.persistenceController = persistenceController
        
        // 创建测试成员
        let user = User(context: testContext)
        user.id = UUID()
        user.nickname = "测试用户"
        user.createdAt = Date()
        
        testMember = Member(context: testContext)
        testMember.id = UUID()
        testMember.name = "测试成员"
        testMember.role = "son"
        testMember.currentPoints = 100
        testMember.totalPoints = 100
        testMember.createdAt = Date()
        testMember.user = user
        
        try testContext.save()
    }
    
    override func tearDownWithError() throws {
        persistenceController = nil
        testContext = nil
        dataManager = nil
        testMember = nil
    }
    
    // MARK: - 盲盒配置保存测试
    
    func testSaveBlindBoxConfig() throws {
        // 准备测试数据
        let boxCount = 5
        let costPerPlay = 20
        let boxPrizes = ["小贴纸", "铅笔", "橡皮", "尺子", "小玩具"]
        
        // 保存盲盒配置
        let savedConfig = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: boxCount,
            costPerPlay: costPerPlay,
            boxPrizes: boxPrizes
        )
        
        // 验证保存结果
        XCTAssertNotNil(savedConfig, "盲盒配置应该保存成功")
        XCTAssertEqual(savedConfig?.itemCount, Int32(boxCount), "盲盒数量应该正确")
        XCTAssertEqual(savedConfig?.costPerPlay, Int32(costPerPlay), "消耗积分应该正确")
        XCTAssertEqual(savedConfig?.toolType, "blindbox", "工具类型应该是盲盒")
        XCTAssertEqual(savedConfig?.member, testMember, "应该关联到正确的成员")
        
        // 验证奖品项目
        let items = savedConfig?.allItems ?? []
        XCTAssertEqual(items.count, boxCount, "奖品项目数量应该正确")
        
        for (index, expectedPrize) in boxPrizes.enumerated() {
            let item = items.first { $0.itemIndex == index }
            XCTAssertNotNil(item, "索引 \(index) 的奖品项目应该存在")
            XCTAssertEqual(item?.prizeName, expectedPrize, "索引 \(index) 的奖品名称应该正确")
        }
    }
    
    // MARK: - 盲盒配置加载测试
    
    func testGetBlindBoxConfig() throws {
        // 先保存一个配置
        let boxCount = 3
        let costPerPlay = 15
        let boxPrizes = ["奖品1", "奖品2", "奖品3"]
        
        let _ = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: boxCount,
            costPerPlay: costPerPlay,
            boxPrizes: boxPrizes
        )
        
        // 加载配置
        let loadedConfig = dataManager.getBlindBoxConfig(for: testMember)
        
        // 验证加载结果
        XCTAssertNotNil(loadedConfig, "应该能够加载盲盒配置")
        XCTAssertEqual(loadedConfig?.itemCount, Int32(boxCount), "盲盒数量应该正确")
        XCTAssertEqual(loadedConfig?.costPerPlay, Int32(costPerPlay), "消耗积分应该正确")
        XCTAssertEqual(loadedConfig?.toolType, "blindbox", "工具类型应该是盲盒")
    }
    
    // MARK: - 盲盒配置更新测试
    
    func testUpdateBlindBoxConfig() throws {
        // 先保存一个配置
        let initialBoxCount = 3
        let initialCostPerPlay = 10
        let initialBoxPrizes = ["奖品1", "奖品2", "奖品3"]
        
        let _ = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: initialBoxCount,
            costPerPlay: initialCostPerPlay,
            boxPrizes: initialBoxPrizes
        )
        
        // 更新配置
        let updatedBoxCount = 4
        let updatedCostPerPlay = 25
        let updatedBoxPrizes = ["新奖品1", "新奖品2", "新奖品3", "新奖品4"]
        
        let updatedConfig = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: updatedBoxCount,
            costPerPlay: updatedCostPerPlay,
            boxPrizes: updatedBoxPrizes
        )
        
        // 验证更新结果
        XCTAssertNotNil(updatedConfig, "配置更新应该成功")
        XCTAssertEqual(updatedConfig?.itemCount, Int32(updatedBoxCount), "更新后的盲盒数量应该正确")
        XCTAssertEqual(updatedConfig?.costPerPlay, Int32(updatedCostPerPlay), "更新后的消耗积分应该正确")
        
        // 验证只有一个配置存在（更新而不是创建新的）
        let allConfigs = testMember.allLotteryConfigs.filter { $0.lotteryToolType == .blindbox }
        XCTAssertEqual(allConfigs.count, 1, "应该只有一个盲盒配置")
        
        // 验证奖品项目已更新
        let items = updatedConfig?.allItems ?? []
        XCTAssertEqual(items.count, updatedBoxCount, "更新后的奖品项目数量应该正确")
        
        for (index, expectedPrize) in updatedBoxPrizes.enumerated() {
            let item = items.first { $0.itemIndex == index }
            XCTAssertNotNil(item, "索引 \(index) 的奖品项目应该存在")
            XCTAssertEqual(item?.prizeName, expectedPrize, "索引 \(index) 的奖品名称应该正确")
        }
    }
    
    // MARK: - 数据验证测试
    
    func testBlindBoxConfigValidation() throws {
        // 测试无效的盲盒数量（超出范围）
        let invalidConfig1 = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: 0, // 小于最小值
            costPerPlay: 10,
            boxPrizes: []
        )
        XCTAssertNil(invalidConfig1, "盲盒数量为0的配置应该保存失败")
        
        let invalidConfig2 = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: 25, // 大于最大值
            costPerPlay: 10,
            boxPrizes: Array(repeating: "奖品", count: 25)
        )
        XCTAssertNil(invalidConfig2, "盲盒数量为25的配置应该保存失败")
        
        // 测试无效的积分值
        let invalidConfig3 = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: 5,
            costPerPlay: -10, // 负数
            boxPrizes: Array(repeating: "奖品", count: 5)
        )
        XCTAssertNil(invalidConfig3, "负积分的配置应该保存失败")
        
        // 测试奖品数量不匹配
        let invalidConfig4 = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: 5,
            costPerPlay: 10,
            boxPrizes: ["奖品1", "奖品2"] // 数量不匹配
        )
        XCTAssertNil(invalidConfig4, "奖品数量不匹配的配置应该保存失败")
        
        // 测试空奖品名称
        let invalidConfig5 = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: 3,
            costPerPlay: 10,
            boxPrizes: ["奖品1", "", "奖品3"] // 包含空名称
        )
        XCTAssertNil(invalidConfig5, "包含空奖品名称的配置应该保存失败")
        
        // 测试过长的奖品名称
        let longPrizeName = String(repeating: "很长的奖品名称", count: 10) // 超过20个字符
        let invalidConfig6 = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: 2,
            costPerPlay: 10,
            boxPrizes: ["正常奖品", longPrizeName]
        )
        XCTAssertNil(invalidConfig6, "包含过长奖品名称的配置应该保存失败")
    }
    
    // MARK: - 边界值测试
    
    func testBlindBoxConfigBoundaryValues() throws {
        // 测试最小有效配置
        let minConfig = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: LotteryConfig.ToolType.blindbox.minItemCount,
            costPerPlay: 0,
            boxPrizes: Array(repeating: "奖品", count: LotteryConfig.ToolType.blindbox.minItemCount)
        )
        XCTAssertNotNil(minConfig, "最小有效配置应该保存成功")
        
        // 测试最大有效配置
        let maxConfig = dataManager.saveBlindBoxConfig(
            for: testMember,
            boxCount: LotteryConfig.ToolType.blindbox.maxItemCount,
            costPerPlay: 999,
            boxPrizes: Array(repeating: "奖品", count: LotteryConfig.ToolType.blindbox.maxItemCount)
        )
        XCTAssertNotNil(maxConfig, "最大有效配置应该保存成功")
    }
    
    // MARK: - 性能测试
    
    func testBlindBoxConfigPerformance() throws {
        // 测试保存配置的性能
        measure {
            for i in 0..<10 {
                let _ = dataManager.saveBlindBoxConfig(
                    for: testMember,
                    boxCount: 5,
                    costPerPlay: 10 + i,
                    boxPrizes: ["奖品1", "奖品2", "奖品3", "奖品4", "奖品5"]
                )
            }
        }
    }
}
