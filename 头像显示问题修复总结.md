# 头像显示问题修复总结

## 问题描述

在道具配置弹窗中，成员头像没有正确显示，而是显示了文件路径，如：
`/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Assets.xcassets/男生头像.imageset/男生头像.png`

## 问题原因

Member模型的`avatarImageName`属性返回的是系统图标名称（如"person.fill"），但在配置弹窗中使用`Image(member.avatarImageName)`时，期望的是Assets.xcassets中的实际图片资源名称。

## 修复方案

### 1. 修复Member模型的avatarImageName属性

**文件**: `ztt2/Models/CoreDataExtensions.swift`

**修复前**:
```swift
var avatarImageName: String {
    switch role ?? "" {
    case "father":
        return "person.fill"
    case "mother":
        return "person.fill"
    case "son":
        return "person.fill"
    case "daughter":
        return "person.fill"
    default:
        return "person.fill"
    }
}
```

**修复后**:
```swift
var avatarImageName: String {
    switch role ?? "" {
    case "father":
        return "爸爸头像"
    case "mother":
        return "妈妈头像"
    case "son":
        return "男生头像"
    case "daughter":
        return "女生头像"
    default:
        return "其他头像"
    }
}
```

### 2. 修复MemberPickerPopupView中的头像显示

**文件**: `ztt2/Views/Components/MemberPickerPopupView.swift`

**修复前**:
```swift
Image(systemName: member.avatarImageName)
```

**修复后**:
```swift
Image(systemName: member.systemAvatarName)
```

## 头像资源映射

项目中的头像资源位于`ztt2/Assets.xcassets`：

| 角色 | 资源名称 |
|------|----------|
| father (爸爸) | 爸爸头像 |
| mother (妈妈) | 妈妈头像 |
| son (儿子) | 男生头像 |
| daughter (女儿) | 女生头像 |
| 其他 | 其他头像 |

## 影响的组件

### ✅ 已修复的组件：

1. **BlindBoxConfigPopupView** - 盲盒配置弹窗
2. **WheelConfigPopupView** - 大转盘配置弹窗  
3. **ScratchCardConfigPopupView** - 刮刮卡配置弹窗
4. **MemberPickerPopupView** - 成员选择弹窗

### ✅ 已确认正确的组件：

1. **FamilyMemberCardView** - 使用`getRoleBasedAvatarName`方法，已正确映射
2. **MemberDetailView** - 使用`getAvatarImageName`方法，已正确映射

## 头像显示的两种方式

### 1. 图片资源方式
用于显示实际的头像图片：
```swift
Image(member.avatarImageName)  // 返回如"男生头像"
```

### 2. 系统图标方式  
用于显示系统图标：
```swift
Image(systemName: member.systemAvatarName)  // 返回如"person.fill"
```

## 验证结果

- ✅ 项目构建成功
- ✅ 所有配置弹窗现在应该正确显示成员头像
- ✅ 不影响其他组件的头像显示
- ✅ 保持了代码的一致性

## 注意事项

1. **avatarImageName属性**：现在返回Assets.xcassets中的实际图片资源名称
2. **systemAvatarName属性**：继续返回系统图标名称，用于需要SF Symbol的场景
3. **向后兼容**：修复不影响现有的正确实现

## 测试建议

建议测试以下场景：
1. 打开盲盒配置弹窗，验证不同角色成员的头像显示
2. 打开大转盘配置弹窗，验证头像显示
3. 打开刮刮卡配置弹窗，验证头像显示
4. 验证成员选择弹窗中的头像显示
5. 确认首页成员卡片头像显示正常
6. 确认成员详情页头像显示正常

## 总结

通过修复Member模型的`avatarImageName`属性，使其返回正确的图片资源名称，解决了配置弹窗中头像显示文件路径的问题。同时保持了系统图标的使用场景，确保了整个应用的头像显示一致性。
